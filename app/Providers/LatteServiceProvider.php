<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Latte\Engine;
use Statamic\Entries\Entry;
use Statamic\Entries\EntryCollection;

class LatteServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $latte = $this->app->get(Engine::class);

        $latte->addFunction('next', function (EntryCollection $collection, string $value) {
            $collection->filter(function (Entry $entry) use ($value) {
                return $entry->get('type') === $value;
            });
        });

        $latte->addFilter('embed_url', function (string $url) {
            if (str_contains($url, 'youtube')) {
                $url = str_contains($url, 'shorts/')
                    ? str_replace('shorts/', 'embed/', $url)
                    : str_replace('watch?v=', 'embed/', $url);
            }

            if (str_contains($url, 'youtu.be')) {
                $url = str_replace('youtu.be', 'www.youtube.com/embed', $url);
            }

            if (str_contains($url, 'vimeo')) {
                $url = str_replace('/vimeo.com', '/player.vimeo.com/video', $url);

                if (! str_contains($url, 'progressive_redirect') && count(explode('/', $url)) > 5) {
                    $hash = substr($url, strrpos($url, '/') + 1);
                    $url = substr($url, 0, strrpos($url, '/')) . '?h=' . str_replace('?', '&', $hash);
                }
            }

            // Make sure additional query parameters are included.
            if (str_contains($url, '&') && !str_contains($url, '?')) {
                $url = str_replace('&', '?', $url);
            }

            return $url;
        });
    }
}
