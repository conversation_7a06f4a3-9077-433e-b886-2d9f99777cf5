<?php

use DekApps\ErrorClient\ErrorHandler;
use DekApps\ErrorClient\Factory;

require_once(__DIR__ . '/vendor/autoload.php');

$errorHandler = new ErrorHandler(__DIR__ . '/storage');
$errorHandler->setNotification('<EMAIL>', 'Error-client: skupina-dek.cz', 4 * 3600);
$errorHandler->setLogger(3 * 60);

$cfg = [
    'logDir' => '/var/log/tracy',
    'privateKeyPath' => __DIR__ . '/ssl/private_key',
    'appId' => 12,
    'sslAlgo' => 9,
];

$manager = Factory::tracy($cfg);
$manager->log();
