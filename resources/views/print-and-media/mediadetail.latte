{layout resource_path('views/layout.latte')}

{block content}
<div class="uk-container uk-container-small">
    <h1 class="page-name">{$page->raw('title')|noescape}</h1>
</div>

<div class='media-detail-navigation'>
    {var $nextNew = Statamic::tag('collection:next')->in('vyrocni-zpravy')->current($page->getId())->type(Statamic::tag('dek_news:getType'))->fetch()}
    {dump next($nextNew)}
    {if ! empty($nextNew)}
        <a href="{Statamic::tag('dek_news:anchor')->url($nextNew->url)}" class="nav-arrow nav-next" style="margin-right: 100px; background-color: green"></a>
    {/if}

    {var $next = Statamic::tag('collection:next')->in('vyrocni-zpravy')->current($page->getId())->where('type', Statamic::tag('dek_news:getType')->fetch())->fetch()->first()}
    {if ! empty($next)}
        <a href="{Statamic::tag('dek_news:anchor')->url($next->url)}" class="nav-arrow nav-next" style="margin-right: 50px"></a>
    {/if}

    {var $next = Statamic::tag('collection:next')->from('vyrocni-zpravy')->where('type', Statamic::tag('dek_news:getType'))->limit(1)->sort('date:asc')->current($page->getId())->get()}
    {var $rendered = false}
    {foreach $next as $entry}
        {php $rendered = true}
        <a href="{Statamic::tag('dek_news:anchor')->url($entry->url)}" class="nav-arrow nav-next"></a>
    {/foreach}

    <a href="{__('routes.media.url')}" class="icon icon-close">
        <svg width="30" height="30" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" data-svg="close">
            <path fill="none" stroke="#fff" stroke-width="2.06" d="M16,16 L4,4"></path>
            <path fill="none" stroke="#fff" stroke-width="2.06" d="M16,4 L4,16"></path>
        </svg>
    </a>

    {var $previous = Statamic::tag('collection:previous')->where('type', Statamic::tag('dek_news:getType')->fetch())->limit(1)->sort('date:asc')->current($page->getId())->fetch()->first()}
    {if ! empty($previous)}
        <a href="{Statamic::tag('dek_news:anchor')->url($previous->url)}" class="nav-arrow nav-back"></a>
    {/if}
{*
    {{ collection:next in="vyrocni-zpravy" limit="1" sort="date:asc" type:contains=NewsAddon:getType}}
    {{ if no_results }}
    {{collection:vyrocni-zpravy limit="1" sort='date:asc' type:contains=NewsAddon:getType }}
    <a href="{{NewsAddon:setAnchorHref url=url}}" class="nav-arrow nav-back"></a>
    {{/collection:vyrocni-zpravy}}
    {{else}}
    <a href="{{NewsAddon:setAnchorHref url=url}}" class="nav-arrow nav-back"></a>
    {{ /if }}
    {{/collection:next}}
    <a href='{{main_addon:translate :lang="locale" type="url" page="media"}}' class="icon icon-close">
        <svg width="30" height="30" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" data-svg="close">
            <path fill="none" stroke="#fff" stroke-width="2.06" d="M16,16 L4,4"></path>
            <path fill="none" stroke="#fff" stroke-width="2.06" d="M16,4 L4,16"></path>
        </svg>
    </a>
    {{ collection:previous in="vyrocni-zpravy" limit="1" sort="date:asc" type:contains=NewsAddon:getType}}
    {{ if no_results }}
    {{collection:vyrocni-zpravy limit="1" sort="date:desc" type:contains=NewsAddon:getType }}
    <a href="{{NewsAddon:setAnchorHref url=url}}"  class="nav-arrow nav-next"></a>
    {{/collection:vyrocni-zpravy}}
    {{else}}
    <a href="{{NewsAddon:setAnchorHref url=url}}" class="nav-arrow nav-next"></a>
    {{ /if }}
    {{/collection:previous}}
*}
</div>

<section class="uk-section uk-section-secondary media-detail">
    <div class="uk-container uk-container-xsmall">
        <div class="redactor-style" data-uk-grid>
            <div class="uk-width-1-1">
                {if $page->raw('intro')}
                    <div class="redactor-style detail-header">
                        {$page->raw('intro')|noescape}
                    </div>
                {/if}
                {if $page->raw('before-image')}
                    <div class="redactor-style detail-header">
                        {$page->raw('before-image')|noescape}
                    </div>
                {/if}
                <div class="uk-align-center detail-image">
                    <img src="{Statamic::tag('glide')->src($page->augmented('image'))->width(800)->fit('max')}" alt="">
                </div>
                <div class="detail-content">
                    {$page->augmented('content')|noescape}
                </div>
                {*<div class="detail-footer">
                <span class="date">{{date format="j. n. Y"}}</span>
                </div>*}
            </div>
        </div>
    </div>
</section>
{/block}
