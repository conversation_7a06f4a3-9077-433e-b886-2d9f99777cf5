{layout resource_path('views/layout.latte')}

{block content}

{if ! request()->has('type') && in_array(app()->getLocale(), ['cz', 'sk'])}
    {Statamic::tag('redirect')->to(url()->current() . '?type=vyrocni-zpravy')}
{elseif ! request()->has('type') && app()->getLocale() === 'en'}
    {Statamic::tag('redirect')->to(url()->current() . '?type=annual')}
{/if}

<div class="uk-container media-list">
    <h1 class="page-name">{$page->raw('title')|noescape}</h1>

    <div id="filters-container">
        <ul class="uk-subnav uk-subnav-pill uk-flex-center" data-uk-margin>
            <li class="{if str_ends_with(url()->current(), __('routes.news-ann.url'))}uk-active{/if}"><a id="news-ann" class="btn" href="{__('routes.news-ann.url')}">{__('routes.news-ann.name')}</a></li>
            <li class="{if str_ends_with(url()->current(), __('routes.news-about.url'))}uk-active{/if}"><a id="news-about" class="btn" href="{__('routes.news-about.url')}">{__('routes.news-about.name')}</a></li>
            <li class="{if str_ends_with(url()->current(), __('routes.news-appr.url'))}uk-active{/if}"><a  id="news-appr" class="btn" href="{__('routes.news-appr.url')}">{__('routes.news-appr.name')}</a></li>
            <li class="{if str_ends_with(url()->current(), __('routes.news-kontakt.url'))}uk-active{/if}"><a  id="news-kontakt" class="btn" href="{__('routes.news-kontakt.url')}">{__('routes.news-kontakt.name')}</a></li>
        </ul>
    </div>

    <div id="hack-height" class="uk-child-width-1-3@m uk-child-width-1-2@s media-boxes" uk-grid="masonry: true" data-uk-height-match="target: > .matched" >
        {foreach Statamic\Facades\Entry::query()->where('collection', 'vyrocni-zpravy')->whereStatus('published')->where('locale', app()->getLocale())->where('type', Statamic::tag('dek_news:getType'))->orderBy('date', 'desc')->get() as $annualReport}
            {include resource_path('views/partials/media_box.latte'), iterator => $iterator, annualReport => $annualReport}
        {/foreach}
    </div>
</div>

<button id="js-showNext" onclick="showNext()" class="btn">{__('contact.btn_next')}</button>

<script src="{asset '/js/media.js'}"></script>
{/block}
