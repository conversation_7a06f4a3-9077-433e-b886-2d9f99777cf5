{"php": "8.2.21", "version": "3.64.0:v3.64.0#58dd9c931c785a79739310aef5178928305ffa67", "indent": "    ", "lineEnding": "\n", "rules": {"blank_line_after_namespace": true, "braces_position": true, "class_definition": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"attribute_placement": "ignore", "on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_import_per_statement": true, "single_line_after_imports": true, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "do", "else", "elseif", "final", "for", "foreach", "function", "if", "interface", "namespace", "private", "protected", "public", "static", "switch", "trait", "try", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "visibility_required": {"elements": ["method", "property"]}, "encoding": true, "full_opening_tag": true, "array_indentation": true, "array_syntax": {"syntax": "short"}, "trailing_comma_in_multiline": {"elements": ["arrays"]}, "binary_operator_spaces": true}, "hashes": {"config/filesystems.php": "772300ef2ac7f60f422a13d272379651", "config/statamic/git.php": "3099bd7f59de4b6916694f52327f6335", "config/statamic/assets.php": "6eb1dc094228094aae9ca65598033571", "config/statamic/cp.php": "7cc44cb62c7e77d24d8fd8b5a898afc6", "config/statamic/system.php": "ac539910bf8bfa9894a63041f8bb6248", "config/statamic/users.php": "a119570b187fb6b00351af794d19a089"}}