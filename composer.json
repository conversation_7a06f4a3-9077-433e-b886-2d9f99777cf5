{"repositories": [{"type": "composer", "url": "https://composer.dek.cz"}], "name": "statamic/statamic", "type": "project", "description": "Statamic", "keywords": ["statamic", "cms", "flat file", "laravel"], "require": {"php": "^8.2", "dek-apps/error-client": "^2.0", "dek-apps/laravel-latte": "^3.1", "dek-apps/laravel-tracy": "^2.0", "dek-apps/mssql-sqlsrv": "^3.5", "dek-statamic/dek-statamic": "^1.0", "dek-statamic/dev-tools": "^1.5", "laravel/framework": "^11.0", "laravel/tinker": "^2.9", "socialiteproviders/dek": "^1.0", "statamic/cms": "^5.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"pre-update-cmd": ["Statamic\\Console\\Composer\\Scripts::preUpdateCmd"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan statamic:install --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\""], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "pixelfear/composer-dist-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}