#!/bin/bash

# ---------------------------------------------
# Functions
# ---------------------------------------------

error()
{
	MSG=$1
	DESC=$2

	#echo -e "\e[101m\n================================================================================"
	echo -e "\e[101m\n"
	echo -e " ERROR: $MSG"
	echo -e "================================================================================"
	if [ "$DESC" ]
	then
		echo -e "\e[101m"
		echo -e "$DESC"
		
	fi
	echo -e "\e[0m"
}

success()
{
	MSG=$1

	echo -e "\e[42m\n"
	echo -e " $MSG "
	echo -e "\e[0m"
	echo
}

git_cmd()
{
	CMD=$1
	echo -e "\e[93m$CMD\e[0m"
	OUTPUT=$($CMD)
	if [[ "$OUTPUT" ]] 
	then 
		echo -e "$OUTPUT"
		
		if [[ $OUTPUT =~ (CONFLICT|conflict) ]]
		then 
			error "Byl zjištěn Konflikt!" "$OUTPUT"
			exit
		fi
		
		if [[ $OUTPUT =~ (ERROR|error) ]]
		then 
			error "Byla zjištěna chyba!" "$OUTPUT"
			exit
		fi
		
	fi
	echo
}


# ---------------------------------------------
# DEPLOY
# ---------------------------------------------

git_output=$(git status)
if [[ $git_output =~ 'Unmerged paths' ]]
then
	error "Byl zjištěn konflikt!" "$git_output"
	exit
fi
if [[ $git_output =~ 'Changes not staged for commit' ]]
then
	error "Byly zjištěny modifikace!" "$git_output"
	exit
fi

read -p "Udelal(a) jsi merge vyvoje do master, tag, push a zablokoval(a) jsi v CP možnost editace a chces pokracovat? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
	git_cmd "git fetch"
	git_cmd "git checkout master"
	git_cmd "git pull"
	git_cmd "git merge --squash origin/production"
	#git_cmd "git commit -m 'Squash merge production - master'"
	echo -e "\e[93mgit commit -m \"Squash merge production -> master\"\e[0m"
	git commit -m "Squash merge production -> master"
	echo
	read -p "Udelat git push? (y/n) " -n 1 -r
	echo
	if [[ $REPLY =~ ^[Yy]$ ]]
	then
		git_cmd "git push"
		PRODUCTION_PUSH=1
	fi
	git_cmd "git checkout production"
	git_cmd "git pull"
	#git_cmd "git merge -m \"Merge branch 'master' into production\" master"
	echo -e "\e[93mgit merge -m \"Merge branch 'master' into production\" master\e[0m"
	git merge -m "Merge branch 'master' into production" master
	echo
	if [[ $PRODUCTION_PUSH -eq 1 ]]
	then
		read -p "Udelat git push a checkout na master? (y/n) " -n 1 -r
		echo
		if [[ $REPLY =~ ^[Yy]$ ]]
		then
			git_cmd "git push"
			git_cmd "git checkout master"
			MASTER_PUSH=1
		fi
	fi
	
	git_cmd "git status"
	
	if [[ $PRODUCTION_PUSH -eq 1 ]]
	then
		if [[ $MASTER_PUSH -eq 1 ]]
			then
				success "Hotovo. Jsi na vetvi master, udelej redeploy."
				exit
			else
				success "Hotovo. Jsi na vetvi production, udelej redeploy a potom checkout na master a push."
				exit
		fi
	else
		success "Hotovo. Jsi na vetvi production, udelej push, pak redeploy a nakonec checkoutni na master a udelej push i tam."
		exit
	fi
fi




