@ECHO OFF
ECHO "Tento script provede zmeny zde v pracovni kopii!"

setlocal
:PROMPT
SET /P AREYOUSURE="Udelal(a) jsi merge vyvoje do master, tag, push a zablokoval(a) jsi v CP moznost editace a chces pokracovat? (y/n) "
IF /I "%AREYOUSURE%" NEQ "Y" GOTO END

	ECHO "git fetch"
	git fetch
	PAUSE
	ECHO "git checkout master" 
	git checkout master
	PAUSE
	ECHO "git pull"
	git pull
	PAUSE
	ECHO "git merge --squash origin/production"
	git merge --squash origin/production
	PAUSE
	ECHO "git commit -m \"Squash merge production -> master\""
	git commit -m "Squash merge production -> master"
	PAUSE
	ECHO "git checkout production"
	git checkout production
	PAUSE
	ECHO "git pull"
	git pull
	PAUSE
	ECHO "git merge -m \"Merge branch 'master' into production\" master"
	git merge -m "Merge branch 'master' into production" master
	PAUSE
	ECHO "git status"
	git status
	ECHO "Hotovo. Jsi na vetvi production, udelej push, pak redeploy a nakonec checkoutni na master a udelej push i tam."

:END
endlocal
PAUSE

